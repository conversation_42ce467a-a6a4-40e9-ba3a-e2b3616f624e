// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Item {
  id        String   @id @default(cuid())
  text      String
  category  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("items")
}
